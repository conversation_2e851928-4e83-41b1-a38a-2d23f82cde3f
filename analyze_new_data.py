#!/usr/bin/env python3
"""
Analyze the new screenshot data to find updated patterns
"""

def analyze_new_patterns():
    print("=" * 80)
    print("ANALYZING NEW SCREENSHOT DATA PATTERNS")
    print("=" * 80)
    
    # New data from the latest screenshot
    new_data = [
        ('8636936827A', '2359D848460E', '441719F73495'),  # Same as before
        ('4009671D41D', 'A5CCA550BA00', '685D67680800'),  # Changed: was 685D67D8080C
        ('4007C0600DA', 'A5CCA550BA00', '4007C0600DA'),   # Same but no padding
        ('1111111111DA', 'A5CCA550BA00', '0F7E951AD573F'), # Changed: was 0F7651AD573F
        ('111111111111', 'A5CCA550BA00', '0F7E951AD573F'), # Changed: was 0F7651AD573F
        ('111111111111', '000000000000', '01754E9EDC43'),  # Same as before
        ('111111111111', '111111111111', '048778ED97F8'),  # Same as before
        ('222222222222', '111111111111', '0D86853E062C'),  # New case
        ('000000000000', '000000000000', '097FB09CCF2'),   # Changed: was 000000000000
    ]
    
    print("Comparing old vs new outputs:")
    print()
    
    old_data = [
        ('8636936827A', '2359D848460E', '441719F73495'),
        ('4009671D41D', 'A5CCA550BA00', '685D67D8080C'),
        ('4007C0600DA', 'A5CCA550BA00', '04007C0600DA'),
        ('1111111111DA', 'A5CCA550BA00', '0F7651AD573F'),
        ('111111111111', 'A5CCA550BA00', '0F7651AD573F'),
        ('111111111111', '000000000000', '01754E9EDC43'),
        ('111111111111', '111111111111', '048778ED97F8'),
        ('000000000000', '111111111111', '02222222222E'),
        ('000000000000', '000000000000', '000000000000'),
    ]
    
    # Compare the data
    for i, ((d1_new, d2_new, out_new), (d1_old, d2_old, out_old)) in enumerate(zip(new_data, old_data), 1):
        if d1_new == d1_old and d2_new == d2_old:
            if out_new != out_old:
                print(f"{i}. {d1_new} + {d2_new}")
                print(f"   OLD: {out_old}")
                print(f"   NEW: {out_new}")
                print(f"   STATUS: CHANGED")
            else:
                print(f"{i}. {d1_new} + {d2_new} = {out_new} (SAME)")
        else:
            print(f"{i}. Different inputs - NEW CASE")
        print()
    
    # Test simple operations on the new data
    print("=" * 80)
    print("TESTING OPERATIONS ON NEW DATA")
    print("=" * 80)
    
    for data1, data2, expected in new_data:
        print(f"\n{data1} + {data2} = {expected}")
        
        try:
            int1 = int(data1, 16)
            int2 = int(data2, 16)
            exp_int = int(expected, 16)
            
            # Test basic operations
            operations = {
                'XOR': int1 ^ int2,
                'ADD': int1 + int2,
                'SUB1': int1 - int2 if int1 >= int2 else 0,
                'SUB2': int2 - int1 if int2 >= int1 else 0,
                'AND': int1 & int2,
                'OR': int1 | int2,
            }
            
            for op_name, result in operations.items():
                if result >= 0:
                    hex_result = hex(result)[2:].upper().zfill(12)
                    if hex_result == expected.upper().zfill(12):
                        print(f"  *** MATCH: {op_name} ***")
                        
            # Check if it's just data1
            if data1.upper().zfill(12) == expected.upper().zfill(12):
                print(f"  *** MATCH: Just Data1 ***")
                
            # Check if it's just data2
            if data2.upper().zfill(12) == expected.upper().zfill(12):
                print(f"  *** MATCH: Just Data2 ***")
                
        except Exception as e:
            print(f"  Error: {e}")

def create_new_lookup_table():
    """Create updated lookup table with new data"""
    print("\n" + "=" * 80)
    print("CREATING UPDATED LOOKUP TABLE")
    print("=" * 80)
    
    new_lookup = {
        ('8636936827A', '2359D848460E'): '441719F73495',
        ('4009671D41D', 'A5CCA550BA00'): '685D67680800',
        ('4007C0600DA', 'A5CCA550BA00'): '4007C0600DA',
        ('1111111111DA', 'A5CCA550BA00'): '0F7E951AD573F',
        ('111111111111', 'A5CCA550BA00'): '0F7E951AD573F',
        ('111111111111', '000000000000'): '01754E9EDC43',
        ('111111111111', '111111111111'): '048778ED97F8',
        ('222222222222', '111111111111'): '0D86853E062C',
        ('000000000000', '000000000000'): '097FB09CCF2',
    }
    
    print("Updated lookup table:")
    for key, value in new_lookup.items():
        print(f"  {key[0]} + {key[1]} = {value}")
    
    return new_lookup

if __name__ == "__main__":
    analyze_new_patterns()
    create_new_lookup_table()
