#!/usr/bin/env python3
"""
Command Line Scientific Calculator for Hexadecimal XOR Operations
Usage: python calculator_cli.py <data1> <data2>
"""

import sys
from test import generate_output, ScientificCalculator

def main():
    if len(sys.argv) == 1:
        # No arguments - show help and examples
        show_help()
        return
    
    if len(sys.argv) == 2 and sys.argv[1].lower() in ['-h', '--help', 'help']:
        show_help()
        return
    
    if len(sys.argv) == 2 and sys.argv[1].lower() in ['-e', '--examples', 'examples']:
        show_examples()
        return
    
    if len(sys.argv) != 3:
        print("Error: Please provide exactly two hexadecimal numbers.")
        print("Usage: python calculator_cli.py <data1> <data2>")
        print("For help: python calculator_cli.py --help")
        sys.exit(1)
    
    data1, data2 = sys.argv[1], sys.argv[2]
    
    # Validate inputs
    calculator = ScientificCalculator()
    if not calculator.validate_input(data1):
        print(f"Error: '{data1}' is not a valid hexadecimal number.")
        sys.exit(1)
    
    if not calculator.validate_input(data2):
        print(f"Error: '{data2}' is not a valid hexadecimal number.")
        sys.exit(1)
    
    # Calculate and display result
    result = generate_output(data1, data2)
    
    print(f"Data1:  {data1.upper()}")
    print(f"Data2:  {data2.upper()}")
    print(f"Output: {result}")
    print(f"Formula: {data1.upper()} XOR {data2.upper()} = {result}")

def show_help():
    print("Scientific Calculator - Hexadecimal XOR Generator")
    print("=" * 50)
    print("Formula: Output = Data1 XOR Data2")
    print()
    print("Usage:")
    print("  python calculator_cli.py <data1> <data2>")
    print("  python calculator_cli.py --examples")
    print("  python calculator_cli.py --help")
    print()
    print("Examples:")
    print("  python calculator_cli.py ABCD 1234")
    print("  python calculator_cli.py 8636936827A 2359D848460E")
    print("  python calculator_cli.py FF00 00FF")
    print()
    print("Input Format:")
    print("  - Hexadecimal numbers (0-9, A-F)")
    print("  - Case insensitive")
    print("  - No '0x' prefix needed")
    print("  - Variable length (auto-padded)")
    print()
    print("Output Format:")
    print("  - 12-character uppercase hexadecimal")
    print("  - Zero-padded if necessary")

def show_examples():
    print("Example Calculations")
    print("=" * 50)
    
    examples = [
        ('8636936827A', '2359D848460E'),
        ('4009671D41D', 'A5CCA550BA00'),
        ('111111111111', '000000000000'),
        ('111111111111', '111111111111'),
        ('ABCD', '1234'),
        ('FF00', '00FF'),
        ('FFFFFFFFFF', '0000000001'),
    ]
    
    calculator = ScientificCalculator()
    results = calculator.batch_calculate(examples)
    
    for i, result in enumerate(results, 1):
        print(f"{i}. {result['data1']} XOR {result['data2']} = {result['output']}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nOperation cancelled.")
        sys.exit(0)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
