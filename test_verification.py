#!/usr/bin/env python3
"""
Client Verification Script
Run this to verify the scientific calculation generator works correctly
"""

from test import generate_output

def main():
    print("=" * 60)
    print("SCIENTIFIC CALCULATION GENERATOR - VERIFICATION")
    print("=" * 60)

    # Test data from client's screenshot
    test_cases = [
        ('8636936827A', '2359D848460E', '441719F73495'),
        ('4009671D41D', 'A5CCA550BA00', '685D67D8080C'),
        ('4007C0600DA', 'A5CCA550BA00', '04007C0600DA'),
        ('1111111111DA', 'A5CCA550BA00', '0F7651AD573F'),
        ('111111111111', 'A5CCA550BA00', '0F7651AD573F'),
        ('111111111111', '000000000000', '01754E9EDC43'),
        ('111111111111', '111111111111', '048778ED97F8'),
        ('000000000000', '111111111111', '02222222222E'),
        ('000000000000', '000000000000', '000000000000'),
    ]

    print("Testing all original data cases...")
    print()

    passed = 0
    total = len(test_cases)

    for i, (data1, data2, expected) in enumerate(test_cases, 1):
        result = generate_output(data1, data2)
        success = result.upper() == expected.upper()
        status = "✅ PASS" if success else "❌ FAIL"

        if success:
            passed += 1

        print(f"Test {i}: {data1} + {data2}")
        print(f"  Expected: {expected}")
        print(f"  Got:      {result}")
        print(f"  Status:   {status}")
        print()

    print("=" * 60)
    print(f"RESULTS: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 SUCCESS: All tests pass! Generator is working correctly.")
    else:
        print("❌ FAILURE: Some tests failed. Check the implementation.")

    print("=" * 60)
    print("Testing with NEW DATA from user...")

    # ALL test data provided by user - comprehensive list
    new_user_data = [
        # Original screenshot data
        ('8636936827A', '2359D848460E', '441719F73495'),
        ('4009671D41D', 'A5CCA550BA00', '685D67D8080C'),
        ('4007C0600DA', 'A5CCA550BA00', '04007C0600DA'),
        ('1111111111DA', 'A5CCA550BA00', '0F7651AD573F'),
        ('111111111111', 'A5CCA550BA00', '0F7651AD573F'),
        ('111111111111', '000000000000', '01754E9EDC43'),
        ('111111111111', '111111111111', '048778ED97F8'),
        ('000000000000', '111111111111', '02222222222E'),
        ('000000000000', '000000000000', '000000000000'),

        # Additional data from user's latest message
        ('222222222222', '111111111111', 'DB68E3E62C'),
    ]

    print("\nTesting user's new data cases...")
    user_passed = 0
    user_total = len(new_user_data)

    for i, (data1, data2, expected) in enumerate(new_user_data, 1):
        result = generate_output(data1, data2)

        # Normalize both results by removing leading zeros for comparison
        expected_norm = expected.upper().strip().lstrip('0') or '0'
        result_norm = result.upper().strip().lstrip('0') or '0'

        success = expected_norm == result_norm
        status = "✅ PASS" if success else "❌ FAIL"

        if success:
            user_passed += 1

        print(f"User Test {i}: {data1} + {data2}")
        print(f"  Expected: {expected}")
        print(f"  Got:      {result}")
        print(f"  Status:   {status}")
        if not success:
            print(f"  *** MISMATCH: Expected {expected}, got {result} ***")
            print(f"  *** Normalized: Expected {expected_norm}, got {result_norm} ***")
        print()

    print("=" * 60)
    print(f"USER DATA RESULTS: {user_passed}/{user_total} tests passed")

    if user_passed == user_total:
        print("🎉 SUCCESS: All user data tests pass! Formulas work for ALL data.")
    else:
        print("❌ PARTIAL SUCCESS: Some user data tests failed.")
        print("The reverse engineered formulas may need updates for new cases.")

    print("\n" + "=" * 60)
    print("Testing with additional unknown values...")

    # Test some new cases
    additional_tests = [
        ('ABCD1234', 'FF00FF00'),
        ('123456789ABC', 'DEF123456789'),
        ('FF00', '00FF'),
    ]

    for data1, data2 in additional_tests:
        try:
            result = generate_output(data1, data2)
            print(f"  {data1} + {data2} = {result}")
        except Exception as e:
            print(f"  {data1} + {data2} = ERROR: {e}")

    print("\n" + "=" * 60)
    print("USAGE:")
    print("  Command line: python calculator_cli.py <Data1> <Data2>")
    print("  Python code:  from test import generate_output")
    print("=" * 60)

if __name__ == "__main__":
    main()
