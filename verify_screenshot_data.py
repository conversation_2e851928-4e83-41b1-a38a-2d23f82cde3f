#!/usr/bin/env python3
"""
Verify all data from the new screenshot
"""

from test import generate_output

def main():
    print("=" * 80)
    print("VERIFYING ALL SCREENSHOT DATA")
    print("=" * 80)
    
    # All data from the new screenshot
    screenshot_data = [
        ('8636936827A', '2359D848460E', '441719F73495'),
        ('4009671D41D', 'A5CCA550BA00', '685D67680800'),  # Note: different from before
        ('4007C0600DA', 'A5CCA550BA00', '4007C0600DA'),
        ('1111111111DA', 'A5CCA550BA00', '0F7E951AD573F'),  # Note: different from before
        ('111111111111', 'A5CCA550BA00', '0F7E951AD573F'),  # Note: different from before
        ('111111111111', '000000000000', '01754E9EDC43'),
        ('111111111111', '111111111111', '048778ED97F8'),
        ('222222222222', '111111111111', '0D86853E062C'),  # New data
        ('000000000000', '000000000000', '097FB09CCF2'),   # Note: different from before
    ]
    
    print("Testing all screenshot data...")
    print()
    
    correct = 0
    total = len(screenshot_data)
    
    for i, (data1, data2, expected) in enumerate(screenshot_data, 1):
        result = generate_output(data1, data2)
        
        # Normalize for comparison
        expected_norm = expected.upper().strip()
        result_norm = result.upper().strip()
        
        is_correct = expected_norm == result_norm
        status = "✅ CORRECT" if is_correct else "❌ WRONG"
        
        if is_correct:
            correct += 1
        
        print(f"{i:2d}. Data1: {data1:>12} | Data2: {data2:>12}")
        print(f"    Expected: {expected_norm}")
        print(f"    Got:      {result_norm}")
        print(f"    Status:   {status}")
        
        if not is_correct:
            print(f"    *** MISMATCH DETECTED ***")
        print()
    
    accuracy = (correct / total) * 100
    print("=" * 80)
    print(f"VERIFICATION RESULTS:")
    print(f"Correct: {correct}/{total}")
    print(f"Accuracy: {accuracy:.1f}%")
    
    if accuracy == 100:
        print("🎉 SUCCESS: All test cases pass!")
        print("✅ The generator works correctly for all screenshot data.")
        
        # Now test the new data pair
        print("\n" + "=" * 80)
        print("CALCULATING NEW DATA PAIR")
        print("=" * 80)
        
        new_data1 = '4009671D47A'
        new_data2 = 'B76E910AE90F'
        
        print(f"Data1: {new_data1}")
        print(f"Data2: {new_data2}")
        
        try:
            result = generate_output(new_data1, new_data2)
            print(f"Output: {result}")
            print(f"\n✅ ANSWER: {new_data1} + {new_data2} = {result}")
        except Exception as e:
            print(f"❌ ERROR: {e}")
            
    else:
        print("❌ ISSUES DETECTED: Some test cases don't match.")
        print("🔧 The algorithm may need updates for the new data.")
        
        # Show which ones failed
        print("\nFailed cases:")
        for i, (data1, data2, expected) in enumerate(screenshot_data, 1):
            result = generate_output(data1, data2)
            if result.upper().strip() != expected.upper().strip():
                print(f"  {i}. {data1} + {data2}")
                print(f"     Expected: {expected}")
                print(f"     Got:      {result}")

if __name__ == "__main__":
    main()
