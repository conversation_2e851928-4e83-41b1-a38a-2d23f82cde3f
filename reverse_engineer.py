#!/usr/bin/env python3
"""
REAL <PERSON>erse Engineering - Find the actual algorithm
No more lookup tables - let's find the real mathematical pattern
"""

def analyze_data():
    # All known data points
    data_points = [
        ('8636936827A', '2359D848460E', '441719F73495'),
        ('4009671D41D', 'A5CCA550BA00', '685D67D8080C'),
        ('4007C0600DA', 'A5CCA550BA00', '04007C0600DA'),
        ('1111111111DA', 'A5CCA550BA00', '0F7651AD573F'),
        ('111111111111', 'A5CCA550BA00', '0F7651AD573F'),
        ('111111111111', '000000000000', '01754E9EDC43'),
        ('111111111111', '111111111111', '048778ED97F8'),
        ('000000000000', '111111111111', '02222222222E'),
        ('000000000000', '000000000000', '000000000000'),
        ('222222222222', '111111111111', 'DB68E3E62C'),
        ('4009671D47A', 'B76E910AE90F', '1B389FD45906'),  # New corrected case
    ]

    print("=== REVERSE ENGINEERING ANALYSIS ===")
    print("Looking for mathematical patterns...\n")

    for i, (data1, data2, expected) in enumerate(data_points, 1):
        print(f"Case {i}: {data1} + {data2} = {expected}")

        # Convert to integers for analysis
        try:
            int1 = int(data1, 16)
            int2 = int(data2, 16)
            exp_int = int(expected, 16)

            print(f"  Decimal: {int1} + {int2} = {exp_int}")

            # Test various operations
            operations = {
                'XOR': int1 ^ int2,
                'ADD': int1 + int2,
                'SUB1': int1 - int2 if int1 >= int2 else int2 - int1,
                'AND': int1 & int2,
                'OR': int1 | int2,
                'MULT': (int1 * int2) % (2**48),
                'ADD_MOD': (int1 + int2) % (2**48),
                'XOR_SHIFT': (int1 ^ int2) >> 1,
                'COMPLEX1': ((int1 + int2) ^ (int1 & int2)) % (2**48),
                'COMPLEX2': ((int1 ^ int2) + (int1 & int2)) % (2**48),
            }

            matches = []
            for op_name, result in operations.items():
                if result == exp_int:
                    matches.append(op_name)
                    print(f"  *** MATCH: {op_name} ***")

            if not matches:
                print("  No simple operation matches")

                # Try more complex patterns
                # Check if it's related to bit manipulation
                bit_ops = {
                    'ROT_XOR': ((int1 << 4) ^ (int2 >> 4)) % (2**48),
                    'BYTE_SWAP': swap_bytes_and_xor(int1, int2),
                    'CHECKSUM': calculate_checksum_variant(int1, int2),
                }

                for op_name, result in bit_ops.items():
                    if result == exp_int:
                        print(f"  *** COMPLEX MATCH: {op_name} ***")

        except Exception as e:
            print(f"  Error analyzing: {e}")

        print()

def swap_bytes_and_xor(int1, int2):
    """Try byte swapping operations"""
    try:
        # Convert to bytes and try various manipulations
        bytes1 = int1.to_bytes(6, 'big')
        bytes2 = int2.to_bytes(6, 'big')

        # Try reversing bytes
        rev1 = int.from_bytes(bytes1[::-1], 'big')
        rev2 = int.from_bytes(bytes2[::-1], 'big')

        return (rev1 ^ rev2) % (2**48)
    except:
        return 0

def calculate_checksum_variant(int1, int2):
    """Try checksum-like calculations"""
    try:
        # Sum of all bytes XORed
        combined = (int1 << 48) | int2
        total = 0
        for i in range(12):  # 12 bytes total
            byte_val = (combined >> (i * 8)) & 0xFF
            total ^= byte_val

        return ((int1 + int2) ^ total) % (2**48)
    except:
        return 0

def deep_bit_analysis():
    """Analyze bit patterns more deeply"""
    print("\n=== DEEP BIT PATTERN ANALYSIS ===")

    # Focus on cases that might reveal patterns
    test_cases = [
        ('8636936827A', '2359D848460E', '441719F73495'),
        ('4009671D47A', 'B76E910AE90F', '1B389FD45906'),
        ('111111111111', '000000000000', '01754E9EDC43'),
        ('111111111111', '111111111111', '048778ED97F8'),
    ]

    for data1, data2, expected in test_cases:
        print(f"\nAnalyzing: {data1} + {data2} = {expected}")

        # Convert to binary for bit analysis
        int1 = int(data1, 16)
        int2 = int(data2, 16)
        exp_int = int(expected, 16)

        print(f"Data1 bits: {bin(int1)[2:].zfill(48)}")
        print(f"Data2 bits: {bin(int2)[2:].zfill(48)}")
        print(f"Output bits:{bin(exp_int)[2:].zfill(48)}")

        # Try nibble-wise operations (4-bit chunks)
        nibble_analysis(int1, int2, exp_int)

        # Try byte-wise operations
        byte_analysis(int1, int2, exp_int)

def nibble_analysis(int1, int2, expected):
    """Analyze 4-bit nibbles"""
    print("  Nibble analysis:")

    # Extract nibbles (4-bit chunks)
    nibbles1 = [(int1 >> (i*4)) & 0xF for i in range(12)]
    nibbles2 = [(int2 >> (i*4)) & 0xF for i in range(12)]
    expected_nibbles = [(expected >> (i*4)) & 0xF for i in range(12)]

    # Check if there's a pattern in nibble operations
    patterns_found = []
    for i in range(12):
        n1, n2, exp_n = nibbles1[i], nibbles2[i], expected_nibbles[i]

        if (n1 ^ n2) == exp_n:
            patterns_found.append(f"Nibble {i}: XOR")
        elif (n1 + n2) % 16 == exp_n:
            patterns_found.append(f"Nibble {i}: ADD_MOD16")
        elif (n1 & n2) == exp_n:
            patterns_found.append(f"Nibble {i}: AND")

    if patterns_found:
        print(f"    Found patterns: {patterns_found}")
    else:
        print("    No consistent nibble patterns")

def byte_analysis(int1, int2, expected):
    """Analyze byte-wise operations"""
    print("  Byte analysis:")

    # Extract bytes
    bytes1 = [(int1 >> (i*8)) & 0xFF for i in range(6)]
    bytes2 = [(int2 >> (i*8)) & 0xFF for i in range(6)]
    expected_bytes = [(expected >> (i*8)) & 0xFF for i in range(6)]

    # Check for byte-wise patterns
    patterns_found = []
    for i in range(6):
        b1, b2, exp_b = bytes1[i], bytes2[i], expected_bytes[i]

        if (b1 ^ b2) == exp_b:
            patterns_found.append(f"Byte {i}: XOR")
        elif (b1 + b2) % 256 == exp_b:
            patterns_found.append(f"Byte {i}: ADD_MOD256")
        elif b1 == exp_b and b2 == 0:
            patterns_found.append(f"Byte {i}: COPY_B1")
        elif b2 == exp_b and b1 == 0:
            patterns_found.append(f"Byte {i}: COPY_B2")

    if patterns_found:
        print(f"    Found patterns: {patterns_found}")
    else:
        print("    No consistent byte patterns")

def test_hypothesis():
    """Test advanced hypotheses"""
    print("\n=== TESTING ADVANCED HYPOTHESES ===")

    # Test if it's a cryptographic hash or checksum
    test_crypto_patterns()

def test_crypto_patterns():
    """Test if this might be a cryptographic operation"""
    print("Testing cryptographic patterns...")

    data_points = [
        ('8636936827A', '2359D848460E', '441719F73495'),
        ('4009671D47A', 'B76E910AE90F', '1B389FD45906'),
    ]

    for data1, data2, expected in data_points:
        print(f"\nCrypto test: {data1} + {data2}")

        # Test if it's related to CRC, MD5 truncated, etc.
        # This might be a custom hash function
        test_custom_hash(data1, data2, expected)

def test_custom_hash(data1, data2, expected):
    """Test custom hash-like operations"""
    combined_input = data1 + data2

    # Try various hash-like operations
    hash_attempts = []

    # Simple checksum variants
    checksum = 0
    for i in range(0, len(combined_input), 2):
        byte_val = int(combined_input[i:i+2], 16)
        checksum ^= byte_val

    print(f"  Simple XOR checksum: {hex(checksum)}")

    # Try polynomial operations (common in CRC)
    # This is getting complex - might need to try different approaches

if __name__ == "__main__":
    analyze_data()
    deep_bit_analysis()
    test_hypothesis()
