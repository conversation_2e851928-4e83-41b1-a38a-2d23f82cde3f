#!/usr/bin/env python3
"""
Comprehensive Test Script for ALL User Data
Tests every single data point provided by the user
"""

from test import generate_output

def main():
    print("=" * 80)
    print("COMPREHENSIVE TEST - ALL USER DATA")
    print("=" * 80)

    # ALL test data from user's image - every single line
    all_user_data = [
        # From the user's screenshot/data
        ('8636936827A', '2359D848460E', '441719F73495'),
        ('4009671D41D', 'A5CCA550BA00', '685D67D8080C'),
        ('4007C0600DA', 'A5CCA550BA00', '04007C0600DA'),
        ('1111111111DA', 'A5CCA550BA00', '0F7651AD573F'),
        ('111111111111', 'A5CCA550BA00', '0F7651AD573F'),
        ('111111111111', '000000000000', '01754E9EDC43'),
        ('111111111111', '111111111111', '048778ED97F8'),
        ('000000000000', '111111111111', '02222222222E'),
        ('000000000000', '000000000000', '000000000000'),
        ('222222222222', '111111111111', 'DB68E3E62C'),
    ]

    print(f"Testing {len(all_user_data)} data cases...")
    print()

    passed = 0
    failed = 0
    total = len(all_user_data)

    for i, (data1, data2, expected) in enumerate(all_user_data, 1):
        try:
            result = generate_output(data1, data2)
            
            # Normalize both results by removing leading zeros for comparison
            expected_norm = expected.upper().strip().lstrip('0') or '0'
            result_norm = result.upper().strip().lstrip('0') or '0'
            
            success = expected_norm == result_norm
            status = "✅ PASS" if success else "❌ FAIL"
            
            if success:
                passed += 1
            else:
                failed += 1
            
            print(f"Test {i:2d}: {data1} + {data2}")
            print(f"         Expected: {expected}")
            print(f"         Got:      {result}")
            print(f"         Status:   {status}")
            
            if not success:
                print(f"         *** MISMATCH ***")
                print(f"         Expected (norm): {expected_norm}")
                print(f"         Got (norm):      {result_norm}")
            print()
            
        except Exception as e:
            failed += 1
            print(f"Test {i:2d}: {data1} + {data2}")
            print(f"         ERROR: {e}")
            print(f"         Status: ❌ ERROR")
            print()

    print("=" * 80)
    print("FINAL RESULTS:")
    print("=" * 80)
    print(f"Total Tests:  {total}")
    print(f"Passed:       {passed}")
    print(f"Failed:       {failed}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    print()

    if passed == total:
        print("🎉 PERFECT SUCCESS! 🎉")
        print("✅ ALL test cases pass!")
        print("✅ The reverse engineered formulas work for 100% of the data!")
        print("✅ Ready for production use!")
    elif passed > 0:
        print("⚠️  PARTIAL SUCCESS")
        print(f"✅ {passed} out of {total} tests pass")
        print("❌ Some cases need attention")
        
        # Show failed cases
        print("\nFailed cases:")
        for i, (data1, data2, expected) in enumerate(all_user_data, 1):
            try:
                result = generate_output(data1, data2)
                expected_norm = expected.upper().strip().lstrip('0') or '0'
                result_norm = result.upper().strip().lstrip('0') or '0'
                if expected_norm != result_norm:
                    print(f"  {i}. {data1} + {data2}")
                    print(f"     Expected: {expected}")
                    print(f"     Got:      {result}")
            except Exception as e:
                print(f"  {i}. {data1} + {data2} - ERROR: {e}")
    else:
        print("❌ COMPLETE FAILURE")
        print("The algorithm needs major updates")

    print("\n" + "=" * 80)
    print("ALGORITHM STATUS:")
    if passed == total:
        print("✅ ALGORITHM FULLY VALIDATED")
        print("✅ Ready for any new input pairs")
        print("✅ Reverse engineering was successful")
    else:
        print("⚠️  ALGORITHM NEEDS UPDATES")
        print("🔧 Additional patterns may need to be discovered")

    print("=" * 80)

if __name__ == "__main__":
    main()
